import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import "../styles/security.css";
import { ChromePicker } from "react-color";
import { useEncryption } from "../contexts/EncryptionContext";

function SecurityPage({ user, darkMode, textSize, applySettings }) {
  const { encryptionEnabled, toggleEncryption } = useEncryption();
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    label: "Weak",
  });

  const [successMessage, setSuccessMessage] = useState("");
  const [error, setError] = useState("");

  // Settings state for theme and text preferences
  const [selectedColor, setSelectedColor] = useState(
    localStorage.getItem("avatarColor") || "#4a6cf7"
  );
  const [showColorPicker, setShowColorPicker] = useState(false);
  const colorPickerRef = useRef(null);
  const [settings, setSettings] = useState({
    theme: darkMode ? "dark" : "light",
    fontSize: textSize || "medium",
    sidebarHoverExpand:
      localStorage.getItem("sidebarHoverExpand") === "true" || false,
  });

  // Predefined avatar colors
  const avatarColors = [
    "#4a6cf7",
    "#f093fb",
    "#4facfe",
    "#43e97b",
    "#38f9d7",
    "#ffecd2",
    "#fcb69f",
    "#a8edea",
    "#fed6e3",
    "#d299c2",
    "#ffeaa7",
    "#fab1a0",
    "#fd79a8",
    "#fdcb6e",
    "#6c5ce7",
    "#a29bfe",
    "#fd79a8",
    "#e17055",
  ];

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm({
      ...passwordForm,
      [name]: value,
    });

    if (name === "newPassword") {
      const score = calculatePasswordStrength(value);
      setPasswordStrength(score);
    }

    // Clear error when user types
    setError("");
  };

  const validatePasswordForm = () => {
    // Clear previous errors
    setError("");

    // Check if current password is provided
    if (!passwordForm.currentPassword) {
      setError("Current password is required");
      return false;
    }

    // Check if new password is provided
    if (!passwordForm.newPassword) {
      setError("New password is required");
      return false;
    }

    // Check if new password is strong enough
    if (passwordStrength.score < 2) {
      setError("New password is too weak");
      return false;
    }

    // Check if passwords match
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setError("Passwords do not match");
      return false;
    }

    return true;
  };

  const handlePasswordFormSubmit = async (e) => {
    e.preventDefault();

    if (!validatePasswordForm()) {
      return;
    }

    try {
      await axios.post(
        "/api/auth/change-password",
        {
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      // Clear form
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Show success message
      setSuccessMessage("Password changed successfully");

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);
    } catch (error) {
      if (error.response) {
        switch (error.response.status) {
          case 401:
            setError("Current password is incorrect");
            break;
          default:
            setError("Failed to change password. Please try again.");
        }
      } else {
        setError("Failed to change password. Please check your connection.");
      }
    }
  };

  const calculatePasswordStrength = (password) => {
    // This is a simple password strength calculator
    // In a real app, you might want to use a more sophisticated algorithm

    if (!password) {
      return { score: 0, label: "Weak" };
    }

    let score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Complexity checks
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    // Determine label based on score
    let label = "Weak";
    if (score >= 5) label = "Strong";
    else if (score >= 3) label = "Good";
    else if (score >= 2) label = "Fair";

    return { score, label };
  };

  const getPasswordStrengthColor = () => {
    switch (passwordStrength.label) {
      case "Strong":
        return "#28a745";
      case "Good":
        return "#4a6cf7";
      case "Fair":
        return "#ffc107";
      default:
        return "#dc3545";
    }
  };

  // Apply font size changes in real-time
  useEffect(() => {
    document.body.classList.remove("text-small", "text-medium", "text-large");
    document.body.classList.add(`text-${settings.fontSize}`);
  }, [settings.fontSize]);

  // Handle click outside of color picker to close it
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        colorPickerRef.current &&
        !colorPickerRef.current.contains(event.target)
      ) {
        setShowColorPicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [colorPickerRef]);

  const handleThemeChange = (theme) => {
    setSettings({ ...settings, theme });

    let isDarkMode;
    if (theme === "system") {
      // Check system preference
      isDarkMode =
        window.matchMedia &&
        window.matchMedia("(prefers-color-scheme: dark)").matches;
    } else {
      isDarkMode = theme === "dark";
    }

    applySettings({ darkMode: isDarkMode });
    localStorage.setItem("theme", theme);
  };

  const handleFontSizeChange = (fontSize) => {
    setSettings({ ...settings, fontSize });
    applySettings({ textSize: fontSize });
    localStorage.setItem("fontSize", fontSize);
  };

  const handleAvatarColorChange = (color) => {
    setSelectedColor(color);
    localStorage.setItem("avatarColor", color);
    // Dispatch event to notify other components about the color change
    document.dispatchEvent(
      new CustomEvent("avatarColorChanged", { detail: { color } })
    );
  };

  // Handle color change from the color picker
  const handleColorPickerChange = (color) => {
    const hexColor = color.hex;
    handleAvatarColorChange(hexColor);
  };

  // Toggle color picker visibility
  const toggleColorPicker = () => {
    setShowColorPicker(!showColorPicker);

    // Add/remove backdrop class to body for mobile
    if (!showColorPicker) {
      document.body.classList.add("color-picker-open");
    } else {
      document.body.classList.remove("color-picker-open");
    }
  };

  // Remove backdrop when component unmounts or color picker closes
  useEffect(() => {
    return () => {
      document.body.classList.remove("color-picker-open");
    };
  }, []);

  // Remove backdrop when color picker closes
  useEffect(() => {
    if (!showColorPicker) {
      document.body.classList.remove("color-picker-open");
    }
  }, [showColorPicker]);

  return (
    <div className="security-container">
      <div className="security-header">
        <h1>Security Settings</h1>
        <p>Manage your security preferences and account settings</p>
      </div>

      {successMessage && (
        <div className="success-message">
          <i className="fas fa-check-circle"></i> {successMessage}
        </div>
      )}

      {error && (
        <div className="error-message">
          <i className="fas fa-exclamation-circle"></i> {error}
        </div>
      )}

      <div className="settings-grid">
        <div className="settings-card slide-in-left stagger-1">
          <div className="settings-card-header">
            <h2>Password Security</h2>
          </div>
          <div className="settings-card-body">
            <form className="password-form" onSubmit={handlePasswordFormSubmit}>
              <div className="form-group">
                <label>Current Password</label>
                <input
                  type="password"
                  name="currentPassword"
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordChange}
                  placeholder="Enter your current password"
                />
              </div>

              <div className="form-group">
                <label>New Password</label>
                <input
                  type="password"
                  name="newPassword"
                  value={passwordForm.newPassword}
                  onChange={handlePasswordChange}
                  placeholder="Enter your new password"
                />
                {passwordForm.newPassword && (
                  <div className="password-strength">
                    <div className="strength-bar-container">
                      <div
                        className="strength-bar"
                        style={{
                          width: `${(passwordStrength.score / 6) * 100}%`,
                          backgroundColor: getPasswordStrengthColor(),
                        }}
                      ></div>
                    </div>
                    <div
                      className="strength-text"
                      style={{ color: getPasswordStrengthColor() }}
                    >
                      {passwordStrength.label}
                    </div>
                  </div>
                )}
              </div>

              <div className="form-group">
                <label>Confirm New Password</label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordChange}
                  placeholder="Confirm your new password"
                />
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">
                  Change Password
                </button>
              </div>
            </form>
          </div>
        </div>

        <div className="settings-card slide-in-right stagger-2">
          <div className="settings-card-header">
            <h2>End-to-End Encryption</h2>
          </div>
          <div className="settings-card-body">
            <div className="setting-group">
              <div className="setting-option">
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={encryptionEnabled}
                    onChange={toggleEncryption}
                  />
                  <span className="slider"></span>
                </label>
                <span className="setting-label">
                  Enable end-to-end encryption
                </span>
              </div>
              <p className="setting-description">
                When enabled, your messages will be encrypted and can only be
                read by you and the recipient. This provides an additional layer
                of security for your communications.
              </p>
            </div>
          </div>
        </div>

        <div className="settings-card slide-in-left stagger-3">
          <div className="settings-card-header">
            <h2>Theme Preferences</h2>
          </div>
          <div className="settings-card-body">
            <div className="setting-group">
              <h3>Theme</h3>
              <p className="setting-description">
                Choose the appearance of the user interface
              </p>
              <div className="theme-options-modern">
                <div
                  className={`theme-option-modern ${
                    settings.theme === "light" ? "active" : ""
                  }`}
                  onClick={() => handleThemeChange("light")}
                >
                  <div className="theme-icon">
                    <i className="fas fa-sun"></i>
                  </div>
                  <div className="theme-content">
                    <div className="theme-title">Light</div>
                    <div className="theme-subtitle">Light theme</div>
                  </div>
                  {settings.theme === "light" && (
                    <div className="theme-check">
                      <i className="fas fa-check"></i>
                    </div>
                  )}
                </div>
                <div
                  className={`theme-option-modern ${
                    settings.theme === "dark" ? "active" : ""
                  }`}
                  onClick={() => handleThemeChange("dark")}
                >
                  <div className="theme-icon">
                    <i className="fas fa-moon"></i>
                  </div>
                  <div className="theme-content">
                    <div className="theme-title">Dark</div>
                    <div className="theme-subtitle">Dark theme</div>
                  </div>
                  {settings.theme === "dark" && (
                    <div className="theme-check">
                      <i className="fas fa-check"></i>
                    </div>
                  )}
                </div>
                <div
                  className={`theme-option-modern ${
                    settings.theme === "system" ? "active" : ""
                  }`}
                  onClick={() => handleThemeChange("system")}
                >
                  <div className="theme-icon">
                    <i className="fas fa-desktop"></i>
                  </div>
                  <div className="theme-content">
                    <div className="theme-title">System</div>
                    <div className="theme-subtitle">Follow system</div>
                  </div>
                  {settings.theme === "system" && (
                    <div className="theme-check">
                      <i className="fas fa-check"></i>
                    </div>
                  )}
                </div>
              </div>
              <div className="current-theme">
                <span>Current theme: </span>
                <span className="current-theme-value">
                  {settings.theme === "light"
                    ? "Light"
                    : settings.theme === "dark"
                    ? "Dark"
                    : "System"}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="settings-card slide-in-right stagger-4">
          <div className="settings-card-header">
            <h2>Text & Avatar Preferences</h2>
          </div>
          <div className="settings-card-body">
            <div className="setting-group">
              <h3>Text Size</h3>
              <div className="font-size-options">
                <div
                  className={`font-size-option ${
                    settings.fontSize === "small" ? "active" : ""
                  }`}
                  onClick={() => handleFontSizeChange("small")}
                >
                  <div className="font-size-preview small">Aa</div>
                  <div className="font-size-label">Small</div>
                </div>
                <div
                  className={`font-size-option ${
                    settings.fontSize === "medium" ? "active" : ""
                  }`}
                  onClick={() => handleFontSizeChange("medium")}
                >
                  <div className="font-size-preview medium">Aa</div>
                  <div className="font-size-label">Medium</div>
                </div>
                <div
                  className={`font-size-option ${
                    settings.fontSize === "large" ? "active" : ""
                  }`}
                  onClick={() => handleFontSizeChange("large")}
                >
                  <div className="font-size-preview large">Aa</div>
                  <div className="font-size-label">Large</div>
                </div>
              </div>
            </div>

            <div className="setting-group">
              <h3>Avatar Color</h3>
              <div className="avatar-color-options">
                {avatarColors.map((color, index) => (
                  <div
                    key={index}
                    className={`avatar-color-option ${
                      selectedColor === color ? "active" : ""
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleAvatarColorChange(color)}
                  >
                    {selectedColor === color && (
                      <i className="fas fa-check"></i>
                    )}
                  </div>
                ))}
              </div>

              <div className="custom-color-option">
                <button
                  className="custom-color-button"
                  onClick={toggleColorPicker}
                >
                  <i className="fas fa-palette"></i> Custom Color
                </button>
                {showColorPicker && (
                  <div className="color-picker-container" ref={colorPickerRef}>
                    <div className="color-picker-header">
                      <span>Choose a color</span>
                      <button
                        className="color-picker-close"
                        onClick={() => setShowColorPicker(false)}
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    </div>
                    <ChromePicker
                      color={selectedColor}
                      onChange={handleColorPickerChange}
                      disableAlpha={true}
                    />
                  </div>
                )}
              </div>

              <div className="avatar-preview">
                <div
                  className="user-avatar-preview"
                  style={{ backgroundColor: selectedColor }}
                >
                  {user && user.name ? user.name.charAt(0).toUpperCase() : "U"}
                </div>
                <div className="avatar-preview-label">Preview</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SecurityPage;

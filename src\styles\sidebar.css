.sidebar {
  width: 80px;
  height: 100vh;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.7)
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-right: 1px solid rgba(224, 224, 224, 0.3);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.08);
  z-index: 999;
}

.sidebar.expanded {
  width: 220px;
}

.dark-mode .sidebar {
  background: linear-gradient(
    135deg,
    rgba(30, 30, 45, 0.9),
    rgba(30, 30, 45, 0.7)
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-right-color: rgba(45, 45, 61, 0.3);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.25);
}

.logo-container {
  padding: 35px 0 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  position: relative;
  overflow: visible;
}

/* Logo styles removed as the image has been removed */

.logo-text {
  font-size: 24px;
  text-align: center;
  margin: 20px 0 0 0;
  font-weight: 600;
  display: none;
  letter-spacing: 1px;
  opacity: 0;
  transform: translateY(5px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  position: relative;
  font-family: "Clash Display", sans-serif;
  padding: 5px 10px;
  gap: 3px;
  justify-content: center;
  border-radius: 10px;
  overflow: visible;
}

.logo-short {
  font-size: 24px;
  text-align: center;
  margin: 20px 0 0 0;
  font-weight: 600;
  letter-spacing: 1px;
  opacity: 1;
  position: relative;
  font-family: "Clash Display", sans-serif;
  padding: 5px 10px;
  display: flex;
  gap: 1px;
  justify-content: center;
  overflow: visible;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.logo-word {
  position: relative;
  background: linear-gradient(
    90deg,
    #4481eb,
    /* Royal blue */ #6a82fb,
    /* Periwinkle */ #05c1ff,
    /* Sky blue */ #6a82fb,
    /* Periwinkle */ #4481eb /* Royal blue */
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: siri-glow 8s ease infinite;
  text-shadow: 0 0 5px rgba(68, 129, 235, 0.1);
  font-family: "Clash Display", sans-serif;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.logo-word::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  filter: blur(10px);
  background: inherit;
  background-clip: text;
  -webkit-background-clip: text;
  opacity: 0.7;
  animation: word-glow 3s ease-in-out infinite alternate;
}

.logo-text::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  border-radius: 10px;
  background: linear-gradient(
    90deg,
    rgba(68, 129, 235, 0.25),
    /* Royal blue */ rgba(106, 130, 251, 0.25),
    /* Periwinkle */ rgba(5, 193, 255, 0.25),
    /* Sky blue */ rgba(106, 130, 251, 0.25),
    /* Periwinkle */ rgba(68, 129, 235, 0.25) /* Royal blue */
  );
  background-size: 300% auto;
  filter: blur(15px);
  opacity: 0.5;
  animation: siri-blur 8s ease infinite;
}

.sidebar.expanded .logo-text {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.sidebar.expanded .logo-short {
  display: none;
  opacity: 0;
}

@keyframes siri-glow {
  0% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.2);
  }
  100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
}

@keyframes word-glow {
  0% {
    filter: blur(8px);
    opacity: 0.6;
  }
  50% {
    filter: blur(12px);
    opacity: 0.8;
  }
  100% {
    filter: blur(8px);
    opacity: 0.6;
  }
}

.dark-mode .logo-word {
  background: linear-gradient(
    90deg,
    #00c6ff,
    /* Electric blue */ #b14aff,
    /* Brighter violet for dark mode */ #ff3a8c,
    /* Magenta */ #b14aff,
    /* Brighter violet for dark mode */ #00c6ff /* Electric blue */
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: siri-glow-dark 8s ease infinite;
  text-shadow: 0 0 8px rgba(0, 198, 255, 0.2);
  font-family: "Clash Display", sans-serif;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.dark-mode .logo-word::after {
  animation: word-glow-dark 3s ease-in-out infinite alternate;
}

@keyframes siri-glow-dark {
  0% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.4);
  }
  100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
}

@keyframes word-glow-dark {
  0% {
    filter: blur(10px);
    opacity: 0.7;
  }
  50% {
    filter: blur(18px);
    opacity: 0.9;
  }
  100% {
    filter: blur(10px);
    opacity: 0.7;
  }
}

@keyframes siri-blur {
  0% {
    background-position: 0% 50%;
    filter: blur(12px);
    opacity: 0.4;
  }
  50% {
    background-position: 100% 50%;
    filter: blur(16px);
    opacity: 0.5;
  }
  100% {
    background-position: 0% 50%;
    filter: blur(12px);
    opacity: 0.4;
  }
}

.user-profile {
  padding: 10px 0 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

.sidebar.expanded .user-profile {
  flex-direction: row;
  justify-content: flex-start;
  padding-left: 20px;
}

.dark-mode .user-profile {
  border-color: transparent;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 22px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 6px 20px rgba(68, 129, 235, 0.4),
    0 0 15px rgba(68, 129, 235, 0.2);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: avatar-wave-glow 4s ease-in-out infinite;
}

.user-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: wave-shine 6s linear infinite;
  z-index: 1;
}

.user-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 28px;
  z-index: 0;
}

/* Additional wave effect */
.user-avatar::before {
  animation: wave-shine 6s linear infinite, wave-pulse 8s ease-in-out infinite;
}

/* Second wave effect */
.user-avatar span {
  position: relative;
  z-index: 2;
}

.user-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 28px;
  z-index: 0;
  animation: wave-ripple 10s ease-in-out infinite;
}

.user-avatar:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 25px rgba(68, 129, 235, 0.5),
    0 0 20px rgba(68, 129, 235, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  animation: avatar-wave-glow-hover 2s ease-in-out infinite;
}

@keyframes wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes avatar-wave-glow {
  0% {
    box-shadow: 0 6px 20px rgba(68, 129, 235, 0.4),
      0 0 15px rgba(68, 129, 235, 0.2);
  }
  50% {
    box-shadow: 0 8px 25px rgba(68, 129, 235, 0.6),
      0 0 20px rgba(68, 129, 235, 0.4);
  }
  100% {
    box-shadow: 0 6px 20px rgba(68, 129, 235, 0.4),
      0 0 15px rgba(68, 129, 235, 0.2);
  }
}

@keyframes avatar-wave-glow-hover {
  0% {
    box-shadow: 0 10px 25px rgba(68, 129, 235, 0.5),
      0 0 20px rgba(68, 129, 235, 0.3);
  }
  50% {
    box-shadow: 0 12px 30px rgba(68, 129, 235, 0.7),
      0 0 25px rgba(68, 129, 235, 0.5);
  }
  100% {
    box-shadow: 0 10px 25px rgba(68, 129, 235, 0.5),
      0 0 20px rgba(68, 129, 235, 0.3);
  }
}

@keyframes wave-pulse {
  0% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 220%;
    height: 220%;
  }
  100% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
}

@keyframes wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.user-name {
  margin-left: 15px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: none;
}

.sidebar.expanded .user-name {
  display: block;
}

.dark-mode .user-name {
  color: #e0e0e0;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 5px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  scrollbar-width: thin;
  scrollbar-color: rgba(68, 129, 235, 0.3) transparent;
  gap: 20px;
}

.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background-color: rgba(68, 129, 235, 0.2);
  border-radius: 10px;
}

.dark-mode .sidebar-nav::-webkit-scrollbar-thumb {
  background-color: rgba(4, 190, 254, 0.2);
}

.nav-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  align-items: center;
}

.nav-group-title {
  font-size: 0.7rem;
  text-transform: uppercase;
  color: #888;
  letter-spacing: 0.5px;
  font-weight: 600;
  opacity: 0;
  height: 0;
  overflow: hidden;
  transition: opacity 0.3s ease, height 0.3s ease;
  text-align: left;
  width: 100%;
  padding: 0 20px;
}

.sidebar.expanded .nav-group-title {
  opacity: 1;
  height: auto;
  margin-bottom: 5px;
  padding-left: 25px;
}

.dark-mode .nav-group-title {
  color: #aaa;
}

.admin-group {
  margin-top: 10px;
  padding-top: 15px;
  position: relative;
}

.admin-group::before {
  content: "";
  position: absolute;
  top: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar.expanded .admin-group::before {
  opacity: 1;
}

.dark-mode .admin-group::before {
  background: rgba(255, 255, 255, 0.05);
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.sidebar.expanded .sidebar-nav ul {
  align-items: flex-start;
  padding-left: 15px;
  padding-right: 15px;
}

.sidebar-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  cursor: pointer;
  color: #555;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background-color: rgba(248, 249, 250, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
}

.sidebar-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Add wave animation to the main navigation buttons */
.sidebar-icon::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(68, 129, 235, 0) 0%,
    rgba(68, 129, 235, 0.1) 50%,
    rgba(68, 129, 235, 0) 100%
  );
  transform: rotate(45deg);
  animation: nav-wave-shine 8s linear infinite;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark-mode .sidebar-icon::after {
  background: linear-gradient(
    45deg,
    rgba(4, 190, 254, 0) 0%,
    rgba(4, 190, 254, 0.1) 50%,
    rgba(4, 190, 254, 0) 100%
  );
}

.sidebar-icon:hover::after,
.sidebar-icon.active::after {
  opacity: 1;
}

@keyframes nav-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.sidebar.expanded .sidebar-icon {
  width: 100%;
  border-radius: 16px;
  padding-left: 15px;
  justify-content: flex-start;
}

.dark-mode .sidebar-icon {
  color: #a0a0c6;
  background-color: rgba(38, 38, 54, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-icon:hover {
  background-color: rgba(240, 244, 255, 0.9);
  color: #4481eb;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(68, 129, 235, 0.2);
  border-color: rgba(68, 129, 235, 0.2);
}

.sidebar-icon:hover::before {
  opacity: 1;
}

.sidebar.expanded .sidebar-icon:hover {
  transform: translateX(5px);
}

/* Sidebar hover expansion animations */
.sidebar:hover {
  width: 220px;
}

.sidebar:hover .sidebar-text {
  display: block;
  animation: slide-in-right 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar:hover .logo-text {
  display: flex;
  opacity: 1;
  transform: translateY(0);
  animation: slide-in-right 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar:hover .logo-short {
  display: none;
  opacity: 0;
}

.sidebar:hover .user-name {
  display: block;
  animation: slide-in-right 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar:hover .nav-group-title {
  opacity: 1;
  height: auto;
  margin-bottom: 5px;
  padding-left: 25px;
  animation: slide-in-right 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar:hover .sidebar-nav ul {
  align-items: flex-start;
  padding-left: 15px;
  padding-right: 15px;
}

.sidebar:hover .sidebar-icon {
  width: 100%;
  border-radius: 16px;
  padding-left: 15px;
  justify-content: flex-start;
}

.sidebar:hover .sidebar-icon i {
  min-width: 24px;
}

.sidebar:hover .sidebar-footer {
  justify-content: flex-start;
  padding-left: 15px;
}

.sidebar:hover .logout-button {
  width: auto;
  padding: 0 20px 0 15px;
  justify-content: flex-start;
}

.sidebar:hover .logout-button i {
  min-width: 24px;
}

.sidebar:hover .user-profile {
  flex-direction: row;
  justify-content: flex-start;
  padding-left: 20px;
}

/* Slide-in animations */
@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.dark-mode .sidebar-icon:hover {
  background-color: rgba(48, 48, 77, 0.9);
  color: #04befe;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  border-color: rgba(4, 190, 254, 0.2);
}

.sidebar-icon.active {
  color: white;
  background: linear-gradient(135deg, #4481eb, #04befe);
  font-weight: 500;
  box-shadow: 0 8px 25px rgba(68, 129, 235, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
}

.sidebar-icon.active::before {
  opacity: 1;
}

.dark-mode .sidebar-icon.active {
  color: white;
  background: linear-gradient(135deg, #04befe, #4481eb);
  box-shadow: 0 8px 25px rgba(4, 190, 254, 0.4);
  border-color: rgba(255, 255, 255, 0.15);
}

.sidebar-icon i {
  font-size: 18px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.05));
}

.sidebar-icon:hover i {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
}

.sidebar.expanded .sidebar-icon i {
  min-width: 24px;
}

.sidebar-text {
  margin-left: 15px;
  font-size: 14px;
  white-space: nowrap;
  font-weight: 500;
  display: none;
  position: relative;
  z-index: 2;
}

.sidebar.expanded .sidebar-text {
  display: block;
}

/* Hide sidebar text on mobile regardless of expanded state */
@media (max-width: 576px) {
  .sidebar .sidebar-text {
    display: none !important;
  }
}

.sidebar-footer {
  padding: 25px 0;
  display: flex;
  justify-content: center;
  margin-top: auto;
  position: relative;
}

.sidebar.expanded .sidebar-footer {
  justify-content: flex-start;
  padding-left: 15px;
}

.dark-mode .sidebar-footer {
  border-color: transparent;
}

.logout-button {
  background: none;
  border: 1px solid rgba(230, 57, 70, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e63946;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background-color: rgba(255, 245, 245, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.logout-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 16px;
}

.sidebar.expanded .logout-button {
  width: auto;
  padding: 0 20px 0 15px;
  justify-content: flex-start;
}

.dark-mode .logout-button {
  color: #ff6b6b;
  background-color: rgba(61, 42, 42, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 107, 107, 0.1);
}

.logout-button:hover {
  background-color: rgba(255, 235, 235, 0.9);
  transform: translateY(-3px) scale(1.08);
  box-shadow: 0 8px 20px rgba(230, 57, 70, 0.3);
  border-color: rgba(230, 57, 70, 0.2);
}

.dark-mode .logout-button:hover {
  background-color: rgba(74, 48, 48, 0.9);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 107, 107, 0.2);
}

.logout-button i {
  font-size: 20px;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.logout-button:hover i {
  transform: scale(1.15);
}

.sidebar.expanded .logout-button i {
  min-width: 24px;
}

/* Modal Dialog (for logout confirmation) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-dialog {
  background-color: white;
  border-radius: 24px;
  width: 340px;
  padding: 30px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  animation: dialog-enter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
}

@keyframes dialog-enter {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dark-mode .modal-dialog {
  background-color: #2d2d3a;
  color: #e0e0e0;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.modal-dialog h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 24px;
  color: #333;
}

.dark-mode .modal-dialog h4 {
  color: #e0e0e0;
}

.modal-dialog p {
  margin-bottom: 30px;
  color: #555;
  line-height: 1.6;
  font-size: 16px;
}

.dark-mode .modal-dialog p {
  color: #b0b0b0;
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn-cancel,
.btn-confirm {
  padding: 12px 24px;
  border-radius: 49px;
  border: none;
  font-weight: 600;
  transition: 0.5s;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
}

.btn-cancel {
  background-color: #f0f0f0;
  color: #555;
}

.dark-mode .btn-cancel {
  background-color: #3a3a48;
  color: #e0e0e0;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark-mode .btn-cancel:hover {
  background-color: #44445a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-confirm {
  background-color: #4481eb;
  color: white;
  transition: 0.5s;
}

.btn-confirm:hover {
  background-color: #04befe;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 15px rgba(4, 190, 254, 0.3);
}

@media (max-width: 1200px) {
  .sidebar {
    width: 220px;
  }

  .sidebar-item span {
    font-size: 0.9rem;
  }

  .sidebar-logo img {
    width: 120px;
  }
}

@media (max-width: 992px) {
  .sidebar {
    width: 200px;
  }

  .sidebar-item {
    padding: 12px 15px;
  }

  .sidebar-logo {
    padding: 15px;
  }

  .sidebar-logo img {
    width: 110px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    height: 100vh;
    position: fixed;
    flex-direction: column;
    justify-content: flex-start;
    padding: 15px 0;
    border-right: 1px solid #eaeaea;
    z-index: 100;
    left: 0;
    top: 0;
    transition: transform 0.3s ease;
    background-color: white;
  }

  .sidebar.collapsed {
    transform: translateX(-60px);
  }

  .dark-mode .sidebar {
    border-right: 1px solid #3a3a48;
    background-color: #1e1e2d;
  }

  .sidebar-logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .sidebar-menu {
    flex-direction: column;
    width: 100%;
    margin-top: 10px;
    align-items: center;
  }

  .sidebar-item {
    margin: 5px 0;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-item span {
    display: none;
  }

  .sidebar-item i {
    margin-right: 0;
    font-size: 1.1rem;
  }

  .sidebar-footer {
    display: flex;
    justify-content: center;
    margin-top: auto;
    padding: 10px 0;
  }
}

@media (max-width: 576px) {
  /* Split the sidebar into two parts: top header and bottom navigation */

  /* Hide section titles completely on mobile */
  .nav-group-title {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Adjust nav group spacing to bring icons closer together */
  .nav-group {
    gap: 5px;
    margin: 0;
    padding: 0;
  }
  .sidebar {
    /* Main sidebar becomes the bottom navigation */
    height: 60px;
    width: 100%;
    flex-direction: row;
    padding: 0;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0.85)
    );
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(224, 224, 224, 0.5);
  }

  .dark-mode .sidebar {
    background: linear-gradient(
      135deg,
      rgba(30, 30, 45, 0.95),
      rgba(30, 30, 45, 0.85)
    );
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.25);
    border-top: 1px solid rgba(45, 45, 61, 0.5);
  }

  .sidebar.expanded {
    height: 60px;
    width: 100%;
  }

  /* Create a clean header like in the chat window */
  .logo-container {
    padding: 0;
    margin: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 60px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
  }

  /* Use the short version (NM) in the header */
  .logo-text {
    display: none !important;
  }

  .logo-short {
    display: flex !important;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    padding: 0 0 0 15px;
    line-height: 1;
  }

  /* Create a clean header bar like in the chat window */
  .header-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    z-index: 999;
    background-color: #f9f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark-mode .header-bar {
    background-color: #222230;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  /* Navigation styles */
  .sidebar-nav {
    flex-direction: row;
    justify-content: center;
    padding: 0;
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%;
    height: 100%;
    /* Set a consistent gap between all items */
    gap: 0;
  }

  /* Make all nav groups consistent width and spacing */
  .nav-group {
    margin: 0;
    padding: 0;
    width: auto;
    display: flex;
    justify-content: center;
  }

  .sidebar-nav ul {
    flex-direction: row;
    justify-content: center;
    padding: 0;
    gap: 0;
    margin: 0;
    width: auto;
    height: 100%;
    align-items: center;
  }

  /* Set uniform spacing for all sidebar icons */
  .sidebar-icon {
    margin: 0;
    padding: 0;
    width: 56px; /* Set fixed width */
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .sidebar-item {
    margin: 0;
    padding: 0;
    width: 56px; /* Set consistent width */
    height: 50px;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
  }

  .sidebar-item::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      #4481eb,
      #6a82fb,
      #05c1ff,
      #6a82fb,
      #4481eb
    );
    transition: width 0.3s ease;
    border-radius: 3px 3px 0 0;
  }

  .sidebar-item.active::after {
    width: 40px;
  }

  .dark-mode .sidebar-item::after {
    background: linear-gradient(
      90deg,
      #00c6ff,
      #b14aff,
      #ff3a8c,
      #b14aff,
      #00c6ff
    );
  }

  .sidebar-item i {
    margin: 0 0 4px 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Show a small label under each icon, but not for .sidebar-text */
  .sidebar-item span:not(.sidebar-text) {
    display: block !important;
    font-size: 10px;
    margin-top: 2px;
    text-align: center;
    color: #666;
  }

  .dark-mode .sidebar-item span:not(.sidebar-text) {
    color: #aaa;
  }

  .sidebar-item.active span:not(.sidebar-text) {
    color: #4a6cf7;
  }

  .dark-mode .sidebar-item.active span:not(.sidebar-text) {
    color: #7a9fff;
  }

  /* Hide user profile completely on mobile */
  .user-profile {
    display: none;
  }

  .sidebar-toggle {
    display: none;
  }

  .sidebar-footer {
    display: none;
  }
}
